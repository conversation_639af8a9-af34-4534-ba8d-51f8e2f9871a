[02:28:00] [<PERSON><PERSON><PERSON>] received Windows console close event; shutting log-core down
[02:28:41] [INFO] received Windows console close event; shutting log-core down
[02:31:36] [INFO] received Windows console close event; shutting log-core down
[04:21:03] [INFO] received Windows console close event; shutting log-core down
[04:24:07] [INF<PERSON>] received Windows console close event; shutting log-core down
[04:28:20] [INFO] received Windows console close event; shutting log-core down
[05:19:33] [INFO] received Windows console close event; shutting log-core down
[05:20:21] [INFO] received Windows console close event; shutting log-core down
[05:27:24] [INFO] received Windows console close event; shutting log-core down
[05:28:08] [INFO] received Windows console close event; shutting log-core down
[05:39:19] [INFO] received Windows console close event; shutting log-core down
[05:39:54] [INFO] received Windows console close event; shutting log-core down
[05:40:23] [INFO] received Windows console close event; shutting log-core down
[05:46:17] [INFO] received Windows console close event; shutting log-core down
[05:46:50] [INFO] received Windows console close event; shutting log-core down
[05:56:46] [INF<PERSON>] received Windows console close event; shutting log-core down
[06:05:20] [INFO] received Windows console close event; shutting log-core down
[06:22:04] [INFO] received Windows console close event; shutting log-core down
[06:40:41] [INFO] received Windows console close event; shutting log-core down
[06:43:04] [INFO] received Windows console close event; shutting log-core down
[06:48:46] [INFO] received Windows console close event; shutting log-core down
[06:51:21] [INFO] received Windows console close event; shutting log-core down
[06:56:16] [INFO] received Windows console close event; shutting log-core down
[07:00:24] [INFO] received Windows console close event; shutting log-core down
[07:01:42] [INFO] received Windows console close event; shutting log-core down
[07:03:05] [INFO] received Windows console close event; shutting log-core down
[07:05:11] [INFO] received Windows console close event; shutting log-core down
[07:22:40] [INFO] received Windows console close event; shutting log-core down
[07:39:49] [INFO] received Windows console close event; shutting log-core down
[07:55:46] [INFO] received Windows console close event; shutting log-core down
[07:56:52] [INFO] received Windows console close event; shutting log-core down
[08:01:33] [INFO] received Windows console close event; shutting log-core down
[08:06:58] [INFO] received Windows console close event; shutting log-core down
[08:09:44] [INFO] received Windows console close event; shutting log-core down
[08:12:26] [INFO] received Windows console close event; shutting log-core down
[08:36:38] [INFO] received Windows console close event; shutting log-core down
[08:53:08] [INFO] received Windows console close event; shutting log-core down
[08:55:40] [INFO] received Windows console close event; shutting log-core down
[09:08:00] [INFO] received Windows console close event; shutting log-core down
[09:18:06] [INFO] received Windows console close event; shutting log-core down
[09:27:37] [INFO] received Windows console close event; shutting log-core down
[09:32:16] [INFO] received Windows console close event; shutting log-core down
[09:41:06] [INFO] received Windows console close event; shutting log-core down
[09:45:16] [INFO] received Windows console close event; shutting log-core down
[09:48:01] [INFO] received Windows console close event; shutting log-core down
[10:07:38] [INFO] received Windows console close event; shutting log-core down
[10:14:33] [INFO] received Windows console close event; shutting log-core down
[20:44:07] [INFO] received Windows console close event; shutting log-core down
[21:12:30] [INFO] received Windows console close event; shutting log-core down
[21:33:51] [INFO] received Windows console close event; shutting log-core down
[21:40:33] [INFO] received Windows console close event; shutting log-core down
[21:51:55] [INFO] received Windows console close event; shutting log-core down
[22:07:23] [INFO] received Windows console close event; shutting log-core down
[22:32:11] [INFO] received Windows console close event; shutting log-core down
[22:38:42] [INFO] received Windows console close event; shutting log-core down
[22:51:16] [INFO] received Windows console close event; shutting log-core down
[22:53:37] [INFO] received Windows console close event; shutting log-core down
[22:56:08] [INFO] received Windows console close event; shutting log-core down
[22:59:34] [INFO] received Windows console close event; shutting log-core down
[23:01:56] [INFO] received Windows console close event; shutting log-core down
[23:04:40] [INFO] received Windows console close event; shutting log-core down
[23:12:17] [INFO] received Windows console close event; shutting log-core down
[23:22:24] [INFO] received Windows console close event; shutting log-core down
[23:25:37] [INFO] received Windows console close event; shutting log-core down
[23:32:19] [INFO] received Windows console close event; shutting log-core down
[00:01:57] [INFO] received Windows console close event; shutting log-core down
[00:29:23] [INFO] received Windows console close event; shutting log-core down
[00:33:55] [INFO] received Windows console close event; shutting log-core down
[00:34:10] [INFO] received Windows console close event; shutting log-core down
[00:53:31] [INFO] received Windows console close event; shutting log-core down
[01:10:37] [INFO] received Windows console close event; shutting log-core down
[01:12:18] [INFO] received Windows console close event; shutting log-core down
[01:16:58] [INFO] received Windows console close event; shutting log-core down
[01:22:36] [INFO] received Windows console close event; shutting log-core down
[01:28:36] [INFO] received Windows console close event; shutting log-core down
[01:29:54] [INFO] received Windows console close event; shutting log-core down
[01:42:25] [INFO] received Windows console close event; shutting log-core down
[01:42:26] [INFO] received Windows console close event; shutting log-core down
[01:47:59] [INFO] received Windows console close event; shutting log-core down
[01:52:46] [INFO] received Windows console close event; shutting log-core down
[01:54:17] [INFO] received Windows console close event; shutting log-core down
[01:58:26] [INFO] received Windows console close event; shutting log-core down
[02:02:28] [INFO] received Windows console close event; shutting log-core down
[02:06:59] [INFO] received Windows console close event; shutting log-core down
[02:12:19] [INFO] received Windows console close event; shutting log-core down
[02:29:17] [INFO] received Windows console close event; shutting log-core down
[04:41:03] [INFO] received Windows console close event; shutting log-core down
[04:48:50] [INFO] received Windows console close event; shutting log-core down
[04:50:31] [INFO] received Windows console close event; shutting log-core down
[04:52:29] [INFO] received Windows console close event; shutting log-core down
[04:56:41] [INFO] received Windows console close event; shutting log-core down
[05:08:10] [INFO] received Windows console close event; shutting log-core down
[05:11:59] [INFO] received Windows console close event; shutting log-core down
[05:16:33] [INFO] received Windows console close event; shutting log-core down
[05:22:55] [INFO] received Windows console close event; shutting log-core down
[05:28:31] [INFO] received Windows console close event; shutting log-core down
[05:35:32] [INFO] received Windows console close event; shutting log-core down
[05:40:47] [INFO] received Windows console close event; shutting log-core down
[05:41:39] [INFO] received Windows console close event; shutting log-core down
[05:42:56] [INFO] received Windows console close event; shutting log-core down
[05:44:01] [INFO] received Windows console close event; shutting log-core down
[05:45:02] [INFO] received Windows console close event; shutting log-core down
[05:49:43] [INFO] received Windows console close event; shutting log-core down
[05:55:02] [INFO] received Windows console close event; shutting log-core down
[05:55:03] [INFO] received Windows console close event; shutting log-core down
[05:58:28] [INFO] received Windows console close event; shutting log-core down
[06:10:40] [INFO] received Windows console close event; shutting log-core down
[06:11:54] [INFO] received Windows console close event; shutting log-core down
[06:15:22] [INFO] received Windows console close event; shutting log-core down
[06:25:55] [INFO] received Windows console close event; shutting log-core down
[06:36:50] [INFO] received Windows console close event; shutting log-core down
[06:50:55] [INFO] received Windows console close event; shutting log-core down
[07:00:15] [INFO] received Windows console close event; shutting log-core down
[07:15:38] [INFO] received Windows console close event; shutting log-core down
[07:28:49] [INFO] received Windows console close event; shutting log-core down
[07:37:53] [INFO] received Windows console close event; shutting log-core down
[07:45:28] [INFO] received Windows console close event; shutting log-core down
[07:46:32] [INFO] received Windows console close event; shutting log-core down
[08:01:20] [INFO] received Windows console close event; shutting log-core down
[08:10:24] [INFO] received Windows console close event; shutting log-core down
[08:13:35] [INFO] received Windows console close event; shutting log-core down
[08:22:45] [INFO] received Windows console close event; shutting log-core down
[08:26:49] [INFO] received Windows console close event; shutting log-core down
[08:37:14] [INFO] received Windows console close event; shutting log-core down
[09:01:33] [INFO] received Windows console close event; shutting log-core down
[09:36:36] [INFO] received Windows console close event; shutting log-core down
[23:03:19] [INFO] received Windows console close event; shutting log-core down
[23:06:51] [INFO] received Windows console close event; shutting log-core down
[23:21:53] [INFO] received Windows console close event; shutting log-core down
[23:30:07] [INFO] received Windows console close event; shutting log-core down
[23:43:46] [INFO] received Windows console close event; shutting log-core down
[23:47:29] [INFO] received Windows console close event; shutting log-core down
[00:20:01] [INFO] received Windows console close event; shutting log-core down
[00:23:02] [INFO] received Windows console close event; shutting log-core down
[00:36:30] [INFO] received Windows console close event; shutting log-core down
[00:45:04] [INFO] received Windows console close event; shutting log-core down
[01:07:13] [INFO] received Windows console close event; shutting log-core down
[01:20:57] [INFO] received Windows console close event; shutting log-core down
[01:26:01] [INFO] received Windows console close event; shutting log-core down
[01:31:34] [INFO] received Windows console close event; shutting log-core down
[01:37:18] [INFO] received Windows console close event; shutting log-core down
[01:38:48] [INFO] received Windows console close event; shutting log-core down
[01:41:01] [INFO] received Windows console close event; shutting log-core down
[02:00:17] [INFO] received Windows console close event; shutting log-core down
[02:07:08] [INFO] received Windows console close event; shutting log-core down
[02:13:07] [INFO] received Windows console close event; shutting log-core down
[02:17:19] [INFO] received Windows console close event; shutting log-core down
[02:22:07] [INFO] received Windows console close event; shutting log-core down
[02:26:47] [INFO] received Windows console close event; shutting log-core down
[03:20:08] [INFO] received Windows console close event; shutting log-core down
[03:32:35] [INFO] received Windows console close event; shutting log-core down
[03:33:55] [INFO] received Windows console close event; shutting log-core down
[03:39:38] [INFO] received Windows console close event; shutting log-core down
[03:42:35] [INFO] received Windows console close event; shutting log-core down
[03:52:38] [INFO] received Windows console close event; shutting log-core down
[03:56:39] [ERROR] exception 0XC0000005 (ACCESS_VIOLATION) from Vectored Exception Handler catched; shutting log-core down
[04:16:54] [ERROR] exception 0XC0000005 (ACCESS_VIOLATION) from Vectored Exception Handler catched; shutting log-core down
[04:30:22] [ERROR] exception 0XC0000005 (ACCESS_VIOLATION) from Vectored Exception Handler catched; shutting log-core down
[04:31:15] [INFO] received Windows console close event; shutting log-core down
[04:48:23] [INFO] received Windows console close event; shutting log-core down
[04:56:36] [INFO] received Windows console close event; shutting log-core down
[05:01:06] [INFO] received Windows console close event; shutting log-core down
[05:43:20] [INFO] received Windows console close event; shutting log-core down
[05:58:20] [INFO] received Windows console close event; shutting log-core down
[06:03:33] [INFO] received Windows console close event; shutting log-core down
[06:12:28] [INFO] received Windows console close event; shutting log-core down
[06:14:20] [INFO] received Windows console close event; shutting log-core down
[06:18:12] [INFO] received Windows console close event; shutting log-core down
[06:20:58] [INFO] received Windows console close event; shutting log-core down
[06:27:40] [INFO] received Windows console close event; shutting log-core down
[06:32:52] [INFO] received Windows console close event; shutting log-core down
[06:33:50] [INFO] received Windows console close event; shutting log-core down
[06:37:11] [INFO] received Windows console close event; shutting log-core down
[06:48:48] [INFO] received Windows console close event; shutting log-core down
[06:58:53] [INFO] received Windows console close event; shutting log-core down
[07:02:08] [INFO] received Windows console close event; shutting log-core down
[07:04:03] [INFO] received Windows console close event; shutting log-core down
[07:25:04] [INFO] received Windows console close event; shutting log-core down
[07:43:58] [INFO] received Windows console close event; shutting log-core down
[08:14:26] [INFO] received Windows console close event; shutting log-core down
[08:17:26] [INFO] received Windows console close event; shutting log-core down
[08:18:55] [INFO] received Windows console close event; shutting log-core down
[08:21:34] [INFO] received Windows console close event; shutting log-core down
[08:30:28] [INFO] received Windows console close event; shutting log-core down
